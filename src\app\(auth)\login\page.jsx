'use client'
import logo from '@/../public/images/Aquapartner_logo.svg'
import bg from '@/../public/images/Background_Login.JPG'
import { auth, useAuth } from '@/app/config/firebaseConfig'
import LoadingScreen from '@/customComponents/LoadingScreen'
import { confirmationResultAtom, errorAtom, recaptchaVerifierAtom } from '@/customComponents/providers'
import { RecaptchaVerifier, signInWithPhoneNumber } from 'firebase/auth'
import { useAtom } from 'jotai'
import Image from 'next/image'
import { useRouter, useSearchParams } from 'next/navigation'
import { Suspense, useEffect, useState } from 'react'

// Component that uses useSearchParams - needs to be wrapped in Suspense
const LoginScreenContent = () => {
  const [mobile, setMobile] = useState('')
  const [otp, setOtp] = useState('')
  const [isOtpSent, setIsOtpSent] = useState(false)
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)

  const [recaptchaVerifier, setRecaptchaVerifier] = useAtom(recaptchaVerifierAtom)
  const [confirmationResult, setConfirmationResult] = useAtom(confirmationResultAtom)

  const [errorMessage, setErrorMessage] = useAtom(errorAtom)

  const user = useAuth()
  const router = useRouter() // Use useRouter for client-side navigation

  const searchParams = useSearchParams()
  const status = searchParams.get('status')

  console.log('Error Message 1: ', errorMessage)

  // Use useEffect to prevent direct setError during render
  useEffect(() => {
    if (status === 'invalidUser') {
      setError('You are not a valid user.')
    }
  }, [status])

  // Redirect user if already logged in
  useEffect(() => {
    console.log('Error message 2: ', errorMessage)
    if (user) {
      router.push('/') // Navigate to home page if user is logged in
    }

    setError(errorMessage?.message ?? '')
  }, [user, router, errorMessage])

  const sendingOtp = async (verifier) => {
    try {
      const result = await signInWithPhoneNumber(auth, `+91${mobile}`, verifier)
      setConfirmationResult(result)
      setIsOtpSent(true)
      console.log('OTP Sent...')
    } catch (error) {
      setError('SMS not sent. Please try again.')
      console.error(error)
      verifier?.clear()
    } finally {
      setLoading(false)
    }
  }

  const sendOtp = async (e) => {
    e.preventDefault()
    setError('')
    setLoading(true)

    if (mobile.length < 10) {
      setError('Please enter a valid mobile number')
      setLoading(false)
      return
    }

    if (!recaptchaVerifier) {
      console.log('Initializing reCAPTCHA verifier')
      try {
        const verifier = new RecaptchaVerifier(auth, 'recaptcha-container', {
          size: 'invisible',
          callback: (response) => {
            console.log('reCAPTCHA verified')
          },
        })

        setRecaptchaVerifier(verifier)
        await sendingOtp(verifier)
      } catch (error) {
        console.error('Error initializing reCAPTCHA verifier:', error)
        setError('Failed to initialize reCAPTCHA. Please try again.')
        setLoading(false)
      }
    } else {
      await sendingOtp(recaptchaVerifier)
    }
  }

  const verifyOtp = async (e) => {
    e.preventDefault()
    setError('')
    setLoading(true)

    if (otp.length !== 6) {
      setError('Please enter a valid 6-digit OTP')
      setLoading(false)
      return
    }

    try {
      if (!confirmationResult) throw new Error('No confirmation result available')

      await confirmationResult.confirm(otp)
      // Redirect or perform additional actions here
      router.push('/')
    } catch (error) {
      setError('Incorrect code. Please try again.')
      console.error(error)
      setLoading(false)
    }
  }

  return (
    <div className="flex min-h-full flex-1">
      {loading && <LoadingScreen />}
      <div className="flex min-h-full flex-1">
        <div className="flex flex-1 flex-col justify-center px-4 py-12 sm:px-6 lg:flex-none lg:px-20 xl:px-24">
          <div className="mx-auto w-full max-w-sm lg:w-96">
            <div>
              <Image alt="Aquapartner Logo" src={logo} className="mb-8 h-8 w-auto" />

              <h2 className="mt-0 leading-9 tracking-tight text-gray-900">Login with</h2>
            </div>

            <div className="">
              {!isOtpSent ? (
                <form onSubmit={sendOtp} className="space-y-6">
                  <div>
                    <label htmlFor="mobile" className="block font-bold leading-6 text-gray-900">
                      Mobile Number
                    </label>
                    <div className="mt-2">
                      <input
                        id="mobile"
                        name="mobile"
                        type="tel"
                        required
                        autoComplete="tel"
                        maxLength={10}
                        value={mobile}
                        onChange={(e) => setMobile(e.target.value)}
                        placeholder="Enter your mobile number"
                        className="block w-full rounded-md border-0 py-1.5 pl-2 placeholder-gray-400 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                      />
                    </div>
                  </div>

                  <div id="recaptcha-container"></div>

                  <div>
                    <button
                      type="submit"
                      className="flex w-full justify-center rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-600"
                    >
                      Send OTP
                    </button>
                  </div>
                  {error && <div className="mb-0 text-sm text-red-600">{error}</div>}
                </form>
              ) : (
                <form onSubmit={verifyOtp} className="space-y-6">
                  <div>
                    <label htmlFor="otp" className="block text-sm font-medium leading-6 text-gray-900">
                      Enter OTP ({mobile})
                    </label>
                    <div className="mt-2">
                      <input
                        id="otp"
                        name="otp"
                        type="text"
                        required
                        value={otp}
                        onChange={(e) => setOtp(e.target.value)}
                        placeholder="Enter the OTP sent to your mobile"
                        className="block w-full rounded-md border-0 py-1.5 pl-2 placeholder-gray-400 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                      />
                    </div>
                  </div>
                  <div className="flex justify-end">
                    <div className="text-sm leading-6">
                      <button
                        type="button"
                        onClick={() => {
                          setMobile('')
                          setIsOtpSent(false)
                        }}
                        className="font-semibold text-indigo-600 hover:text-indigo-500"
                      >
                        Edit Mobile Number?
                      </button>
                    </div>
                  </div>
                  <div>
                    <button
                      type="submit"
                      className="flex w-full justify-center rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-600"
                    >
                      Verify OTP
                    </button>
                  </div>
                </form>
              )}
            </div>
          </div>
        </div>
        <div className="relative hidden w-0 flex-1 lg:block">
          <Image alt="bg" src={bg} className="absolute inset-0 h-full w-full object-cover" fill quality={10} />
        </div>
      </div>
    </div>
  )
}

// Main component with Suspense boundary
const LoginScreen = () => {
  return (
    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
      <LoginScreenContent />
    </Suspense>
  )
}

export default LoginScreen
