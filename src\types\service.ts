// Service-specific types for internal use

import type { PaymentTransaction, PaymentStatus, RefundStatus } from './payment'

// Database connection types
export interface DatabaseConfig {
  uri: string
  options?: {
    useUnifiedTopology?: boolean
    useNewUrlParser?: boolean
  }
}

// Environment configuration
export interface EnvironmentConfig {
  NODE_ENV: 'development' | 'production' | 'test'
  MONGODB_URI: string
  NEXT_PUBLIC_DOMAIN: string
  NEXT_PUBLIC_API_DOMAIN: string
  
  // Zoho OAuth Configuration
  ZOHO_OAUTH_CLIENT_ID: string
  ZOHO_OAUTH_CLIENT_SECRET: string
  ZOHO_OAUTH_REFRESH_TOKEN: string
  
  // Zoho Payment Configuration
  ZOHO_PAYMENT_SESSION_URL: string
  ZOHO_PAY_ACCOUNT_ID: string
  ZOHO_PAY_API_KEY: string
  
  // Webhook Configuration
  ZOHO_WEBHOOK_SECRET: string
}

// Service method options
export interface ServiceOptions {
  timeout?: number
  retries?: number
  validateInput?: boolean
  logRequests?: boolean
}

// Payment service internal types
export interface PaymentServiceConfig {
  baseURL: string
  authURL: string
  accountId: string
  timeout: number
  retries: number
}

export interface TokenInfo {
  access_token: string
  refresh_token?: string
  expires_at?: Date
  token_type?: string
  scope?: string
}

export interface PaymentSessionData {
  amount: number
  currency: string
  description: string
  invoice_number: string
  customer_id: string
  customer_name?: string
  customer_email?: string
  customer_phone?: string
  redirect_url?: string
  reference_id?: string
  meta_data?: Array<{ key: string; value: string }>
}

export interface ZohoApiResponse<T = any> {
  code: number
  message: string
  data?: T
  error?: {
    code: string
    message: string
    details?: any
  }
}

export interface ZohoPaymentSession {
  payments_session_id: string
  amount: number
  currency: string
  status: string
  created_time: number
  expires_at: number
  payment_url: string
  account_id: string
}

export interface ZohoPayment {
  payment_id: string
  payments_session_id: string
  amount: number
  currency: string
  status: PaymentStatus
  payment_method?: string
  created_time: number
  completed_time?: number
  error_code?: string
  error_message?: string
  customer_details?: {
    customer_id: string
    customer_email?: string
    customer_phone?: string
  }
}

export interface ZohoRefund {
  refund_id: string
  payment_id: string
  amount: number
  currency: string
  status: RefundStatus
  reason?: string
  created_time: number
  processed_time?: number
}

// Database model interfaces
export interface PaymentTransactionModel extends PaymentTransaction {
  updateStatus(status: PaymentStatus, data?: any): Promise<void>
  addRefund(refundData: any): Promise<void>
  isExpired(): boolean
  canBeRefunded(): boolean
}

export interface WebhookEventModel {
  _id: string
  event_id: string
  event_type: string
  payment_session_id: string
  payment_id?: string
  amount: number
  currency: string
  customer_id?: string
  customer_email?: string
  transaction_id?: string
  invoice_number?: string
  webhook_received_at: Date
  processed: boolean
  processed_at?: Date
  processing_attempts: number
  raw_data: any
  signature_verified: boolean
  processing_error?: string
  retry_after?: Date
  createdAt: Date
  updatedAt: Date
  
  // Methods
  markAsProcessed(): Promise<void>
  incrementProcessingAttempts(): Promise<void>
}

// Service error types
export interface ServiceError extends Error {
  code?: string
  statusCode?: number
  details?: any
  retryable?: boolean
}

export interface ValidationError extends ServiceError {
  field?: string
  value?: any
  constraint?: string
}

export interface ZohoApiError extends ServiceError {
  zohoCode?: string
  zohoMessage?: string
  requestId?: string
}

// Webhook processing types
export interface WebhookProcessingResult {
  success: boolean
  processed: boolean
  error?: string
  transaction_updated: boolean
  status_changed: boolean
  previous_status?: PaymentStatus
  new_status?: PaymentStatus
}

export interface WebhookValidationResult {
  valid: boolean
  signature_verified: boolean
  error?: string
  event_type?: string
  payment_session_id?: string
}

// Query and filter types
export interface TransactionQuery {
  customer_id?: string | string[]
  status?: PaymentStatus | PaymentStatus[]
  amount?: {
    min?: number
    max?: number
  }
  date_range?: {
    from?: Date
    to?: Date
  }
  invoice_numbers?: string[]
  payment_session_ids?: string[]
}

export interface QueryOptions {
  page?: number
  limit?: number
  sort?: {
    field: string
    order: 'asc' | 'desc'
  }
  populate?: string[]
  select?: string[]
}

export interface PaginatedResult<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
    hasNext: boolean
    hasPrev: boolean
  }
  query?: any
}

// Health check types
export interface ServiceHealthCheck {
  name: string
  status: 'healthy' | 'unhealthy' | 'degraded'
  message: string
  details?: any
  response_time?: number
  last_checked: Date
}

export interface SystemHealth {
  overall_status: 'healthy' | 'unhealthy' | 'degraded'
  timestamp: Date
  version: string
  uptime: number
  checks: {
    database: ServiceHealthCheck
    zoho_api: ServiceHealthCheck
    zoho_auth: ServiceHealthCheck
    webhook_processing: ServiceHealthCheck
  }
  configuration: {
    environment: string
    domain: string
    account_id_configured: boolean
    webhook_secret_configured: boolean
  }
}

// Logging types
export interface LogEntry {
  timestamp: Date
  level: 'debug' | 'info' | 'warn' | 'error'
  message: string
  context?: {
    service: string
    method?: string
    payment_session_id?: string
    customer_id?: string
    transaction_id?: string
    request_id?: string
  }
  data?: any
  error?: {
    name: string
    message: string
    stack?: string
  }
}

// Metrics types
export interface PaymentMetrics {
  total_transactions: number
  successful_payments: number
  failed_payments: number
  pending_payments: number
  total_amount: number
  average_amount: number
  success_rate: number
  failure_rate: number
  processing_time: {
    average: number
    median: number
    p95: number
    p99: number
  }
  period: {
    start: Date
    end: Date
  }
}

export interface WebhookMetrics {
  total_events: number
  processed_events: number
  failed_events: number
  pending_events: number
  processing_rate: number
  average_processing_time: number
  event_types: Record<string, number>
  recent_activity: {
    last_hour: number
    last_24_hours: number
    last_7_days: number
  }
}

// Cache types
export interface CacheEntry<T = any> {
  key: string
  value: T
  expires_at: Date
  created_at: Date
}

export interface CacheOptions {
  ttl?: number // Time to live in seconds
  refresh?: boolean // Whether to refresh if exists
  serialize?: boolean // Whether to serialize the value
}

// Rate limiting types
export interface RateLimitConfig {
  window: number // Time window in seconds
  max_requests: number // Maximum requests per window
  skip_successful?: boolean // Skip counting successful requests
  key_generator?: (req: any) => string // Custom key generator
}

export interface RateLimitInfo {
  limit: number
  remaining: number
  reset: Date
  retry_after?: number
}
