// Payment API Types for Zoho Payment Integration

export interface PaymentRequest {
  amount: number
  currency?: string
  description: string
  invoice_number: string
  customer_id: string
  customer_name?: string
  customer_email?: string
  customer_phone?: string
  redirect_url?: string
  reference_id?: string
  meta_data?: MetaData[]
}

export interface MetaData {
  key: string
  value: string
}

export interface PaymentSession {
  payments_session_id: string
  amount: number
  currency: string
  status: PaymentStatus
  created_time: number
  expires_at: number
  payment_url: string
}

export interface PaymentSessionResponse {
  success: boolean
  message: string
  data: {
    payment_session: PaymentSession
    transaction_id: string
    expires_in: string
  }
}

export interface PaymentStatusResponse {
  success: boolean
  message: string
  data: {
    payment_session: PaymentSessionDetails
    transaction?: TransactionDetails
  }
}

export interface PaymentSessionDetails {
  payments_session_id: string
  amount: number
  currency: string
  status: PaymentStatus
  payment_id?: string
  payment_method?: string
  created_time: number
  completed_time?: number
  error_code?: string
  error_message?: string
}

export interface TransactionDetails {
  id: string
  customer_id: string
  customer_name?: string
  reference_id?: string
  created_at: string
  updated_at: string
}

export interface PaymentTransaction {
  _id: string
  payments_session_id: string
  payment_id?: string
  amount: number
  currency: string
  description: string
  invoice_number: string
  customer_id: string
  customer_name?: string
  customer_email?: string
  customer_phone?: string
  status: PaymentStatus
  meta_data?: MetaData[]
  redirect_url?: string
  reference_id?: string
  session_created_time: Date
  session_expires_at: Date
  payment_method?: string
  completed_time?: Date
  error_code?: string
  error_message?: string
  refunds: RefundDetails[]
  created_at: Date
  updated_at: Date
}

export interface RefundRequest {
  payment_id: string
  amount: number
  reason?: string
  reference_id?: string
}

export interface RefundDetails {
  refund_id: string
  amount: number
  status: RefundStatus
  created_at: Date
  reason?: string
}

export interface RefundResponse {
  success: boolean
  message: string
  data: {
    refund_id: string
    payment_id: string
    amount: number
    currency: string
    status: RefundStatus
    reason?: string
    created_time: number
    reference_id?: string
  }
}

export interface PaymentListResponse {
  success: boolean
  message: string
  data: {
    transactions: PaymentTransaction[]
    pagination: {
      page: number
      limit: number
      total: number
      pages: number
    }
    filters?: {
      customer_id?: string
      status?: PaymentStatus
      from_date?: string
      to_date?: string
    }
  }
}

export interface HealthCheckResponse {
  timestamp: string
  service: string
  version: string
  status: 'healthy' | 'unhealthy'
  checks: {
    database: HealthCheck
    zoho_token: HealthCheck
    zoho_api?: HealthCheck
  }
  configuration: {
    account_id: string
    webhook_secret: string
    domain: string
  }
}

export interface HealthCheck {
  status: 'healthy' | 'unhealthy'
  message: string
  error?: string
  expires_at?: string
  response_time?: string
}

export interface WebhookEvent {
  event_type: WebhookEventType
  payment_session_id: string
  payment_id?: string
  status: PaymentStatus
  amount: number
  currency: string
  payment_method?: string
  created_time: number
  error_code?: string
  error_message?: string
  customer_details?: {
    customer_id: string
    customer_email?: string
  }
  meta_data?: MetaData[]
}

export interface WebhookResponse {
  success: boolean
  message: string
  data: {
    event_type: WebhookEventType
    payment_session_id: string
    status: PaymentStatus
    processed_at: string
  }
}

export interface PaymentError {
  error: string
  message: string
  details?: string
  required_fields?: string[]
  payment_status?: PaymentStatus
  payment_amount?: number
  requested_amount?: number
}

// Enums and Union Types
export type PaymentStatus = 
  | 'created'
  | 'pending'
  | 'succeeded'
  | 'failed'
  | 'cancelled'
  | 'expired'

export type RefundStatus = 
  | 'pending'
  | 'succeeded'
  | 'failed'
  | 'cancelled'

export type WebhookEventType = 
  | 'payment.succeeded'
  | 'payment.failed'
  | 'payment.pending'
  | 'payment.cancelled'
  | 'payment_session.expired'

// Legacy API Types for backward compatibility
export interface LegacyPaymentRequest {
  amount: number
  invoiceNo: string
  redirectUrl?: string
  referenceId?: string
  customerId?: string
  customerName?: string
  customerEmail?: string
  customerPhone?: string
}

export interface LegacyPaymentResponse {
  result: 'success' | 'error'
  paymentSession: PaymentSession
  transaction_id: string
  payment_session_id: string
  expires_in: string
  paymentSessionId: string
}

// Service Configuration Types
export interface ZohoPaymentConfig {
  baseURL: string
  authURL: string
  accountId: string
  clientId?: string
  clientSecret?: string
  refreshToken?: string
}

// API Response Wrapper
export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error?: string
  details?: string
}

// Pagination Types
export interface PaginationOptions {
  page?: number
  limit?: number
  status?: PaymentStatus
  from_date?: string
  to_date?: string
}

export interface AdvancedSearchOptions {
  customer_ids?: string[]
  statuses?: PaymentStatus[]
  amount_range?: {
    min?: number
    max?: number
  }
  date_range?: {
    from?: string
    to?: string
  }
  invoice_numbers?: string[]
  page?: number
  limit?: number
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}
