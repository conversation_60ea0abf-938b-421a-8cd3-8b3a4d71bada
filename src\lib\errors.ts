// Error handling utilities for payment service

export class PaymentError extends Error {
  public readonly code: string
  public readonly statusCode: number
  public readonly details?: any
  public readonly retryable: boolean

  constructor(
    message: string,
    code: string = 'PAYMENT_ERROR',
    statusCode: number = 500,
    details?: any,
    retryable: boolean = false
  ) {
    super(message)
    this.name = 'PaymentError'
    this.code = code
    this.statusCode = statusCode
    this.details = details
    this.retryable = retryable
  }

  toJSON() {
    return {
      error: this.code,
      message: this.message,
      details: this.details,
      retryable: this.retryable,
    }
  }
}

export class ValidationError extends PaymentError {
  public readonly field?: string
  public readonly value?: any
  public readonly constraint?: string

  constructor(
    message: string,
    field?: string,
    value?: any,
    constraint?: string
  ) {
    super(message, 'VALIDATION_ERROR', 400, { field, value, constraint }, false)
    this.name = 'ValidationError'
    this.field = field
    this.value = value
    this.constraint = constraint
  }
}

export class ZohoApiError extends PaymentError {
  public readonly zohoCode?: string
  public readonly zohoMessage?: string
  public readonly requestId?: string

  constructor(
    message: string,
    zohoCode?: string,
    zohoMessage?: string,
    requestId?: string,
    statusCode: number = 500
  ) {
    super(
      message,
      'ZOHO_API_ERROR',
      statusCode,
      { zohoCode, zohoMessage, requestId },
      statusCode >= 500 // Server errors are retryable
    )
    this.name = 'ZohoApiError'
    this.zohoCode = zohoCode
    this.zohoMessage = zohoMessage
    this.requestId = requestId
  }
}

export class AuthenticationError extends PaymentError {
  constructor(message: string = 'Authentication failed') {
    super(message, 'AUTHENTICATION_ERROR', 401, null, false)
    this.name = 'AuthenticationError'
  }
}

export class AuthorizationError extends PaymentError {
  constructor(message: string = 'Insufficient permissions') {
    super(message, 'AUTHORIZATION_ERROR', 403, null, false)
    this.name = 'AuthorizationError'
  }
}

export class NotFoundError extends PaymentError {
  constructor(resource: string, identifier?: string) {
    const message = identifier 
      ? `${resource} with identifier '${identifier}' not found`
      : `${resource} not found`
    super(message, 'NOT_FOUND', 404, { resource, identifier }, false)
    this.name = 'NotFoundError'
  }
}

export class ConflictError extends PaymentError {
  constructor(message: string, details?: any) {
    super(message, 'CONFLICT', 409, details, false)
    this.name = 'ConflictError'
  }
}

export class RateLimitError extends PaymentError {
  public readonly retryAfter?: number

  constructor(message: string = 'Rate limit exceeded', retryAfter?: number) {
    super(message, 'RATE_LIMIT_EXCEEDED', 429, { retryAfter }, true)
    this.name = 'RateLimitError'
    this.retryAfter = retryAfter
  }
}

export class DatabaseError extends PaymentError {
  constructor(message: string, details?: any) {
    super(message, 'DATABASE_ERROR', 500, details, true)
    this.name = 'DatabaseError'
  }
}

export class WebhookError extends PaymentError {
  public readonly eventType?: string
  public readonly paymentSessionId?: string

  constructor(
    message: string,
    eventType?: string,
    paymentSessionId?: string,
    details?: any
  ) {
    super(
      message,
      'WEBHOOK_ERROR',
      500,
      { eventType, paymentSessionId, ...details },
      true
    )
    this.name = 'WebhookError'
    this.eventType = eventType
    this.paymentSessionId = paymentSessionId
  }
}

// Error factory functions
export const createValidationError = (field: string, value: any, constraint: string) => {
  return new ValidationError(
    `Validation failed for field '${field}': ${constraint}`,
    field,
    value,
    constraint
  )
}

export const createZohoApiError = (response: any, requestId?: string) => {
  const message = response?.message || 'Zoho API request failed'
  const zohoCode = response?.code || response?.error_code
  const zohoMessage = response?.error_message || response?.details
  const statusCode = response?.status || 500

  return new ZohoApiError(message, zohoCode, zohoMessage, requestId, statusCode)
}

export const createNotFoundError = (resource: string, identifier?: string) => {
  return new NotFoundError(resource, identifier)
}

export const createDatabaseError = (operation: string, error: Error) => {
  return new DatabaseError(
    `Database operation '${operation}' failed: ${error.message}`,
    { operation, originalError: error.message }
  )
}

// Error handler middleware for API routes
export const handleApiError = (error: Error) => {
  console.error('API Error:', error)

  if (error instanceof PaymentError) {
    return new Response(JSON.stringify(error.toJSON()), {
      status: error.statusCode,
      headers: { 'Content-Type': 'application/json' }
    })
  }

  // Handle mongoose/mongodb errors
  if (error.name === 'ValidationError') {
    return new Response(
      JSON.stringify({
        error: 'VALIDATION_ERROR',
        message: 'Database validation failed',
        details: error.message,
      }),
      { status: 400, headers: { 'Content-Type': 'application/json' } }
    )
  }

  if (error.name === 'MongoError' || error.name === 'MongooseError') {
    return new Response(
      JSON.stringify({
        error: 'DATABASE_ERROR',
        message: 'Database operation failed',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined,
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }

  // Handle fetch/network errors
  if (error.name === 'TypeError' && error.message.includes('fetch')) {
    return new Response(
      JSON.stringify({
        error: 'NETWORK_ERROR',
        message: 'External API request failed',
        details: 'Unable to connect to payment service',
        retryable: true,
      }),
      { status: 503, headers: { 'Content-Type': 'application/json' } }
    )
  }

  // Generic error handler
  return new Response(
    JSON.stringify({
      error: 'INTERNAL_SERVER_ERROR',
      message: 'An unexpected error occurred',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined,
    }),
    { status: 500, headers: { 'Content-Type': 'application/json' } }
  )
}

// Async error wrapper for API routes
export const withErrorHandling = (handler: Function) => {
  return async (...args: any[]) => {
    try {
      return await handler(...args)
    } catch (error) {
      return handleApiError(error as Error)
    }
  }
}

// Error logging utility
export const logError = (error: Error, context?: any) => {
  const logEntry = {
    timestamp: new Date().toISOString(),
    level: 'error',
    message: error.message,
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
    },
    context,
  }

  console.error('Payment Service Error:', JSON.stringify(logEntry, null, 2))

  // In production, you might want to send this to a logging service
  // like Winston, Sentry, or CloudWatch
}

// Retry utility for retryable errors
export const withRetry = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: Error

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error as Error

      // Don't retry if it's not a retryable error
      if (error instanceof PaymentError && !error.retryable) {
        throw error
      }

      // Don't retry on the last attempt
      if (attempt === maxRetries) {
        break
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * attempt))
    }
  }

  throw lastError!
}

// Circuit breaker pattern for external API calls
export class CircuitBreaker {
  private failures: number = 0
  private lastFailureTime: number = 0
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED'

  constructor(
    private readonly threshold: number = 5,
    private readonly timeout: number = 60000 // 1 minute
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.timeout) {
        this.state = 'HALF_OPEN'
      } else {
        throw new PaymentError(
          'Circuit breaker is OPEN - service temporarily unavailable',
          'CIRCUIT_BREAKER_OPEN',
          503,
          null,
          true
        )
      }
    }

    try {
      const result = await operation()
      this.onSuccess()
      return result
    } catch (error) {
      this.onFailure()
      throw error
    }
  }

  private onSuccess() {
    this.failures = 0
    this.state = 'CLOSED'
  }

  private onFailure() {
    this.failures++
    this.lastFailureTime = Date.now()

    if (this.failures >= this.threshold) {
      this.state = 'OPEN'
    }
  }

  getState() {
    return {
      state: this.state,
      failures: this.failures,
      lastFailureTime: this.lastFailureTime,
    }
  }
}
