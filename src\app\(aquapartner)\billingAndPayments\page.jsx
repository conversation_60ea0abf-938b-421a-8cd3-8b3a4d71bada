'use client'
import { Select } from '@/components/select'
import { useRouter, useSearchParams } from 'next/navigation'
import { Suspense } from 'react'
import { CreditNotesPage } from './components/creditNotesPage'
import { DuesPage } from './components/duesPage'
import { InvoicesPage } from './components/invoicesPage'
import { SalesOrderPage } from './components/salesOrderPage'
import { TransactionsPage } from './components/transactionsPage'

const tabs = [
  { name: 'Sales Orders', href: '?page=Sales Orders' },
  { name: 'Dues', href: '?page=Dues' },
  { name: 'Invoices', href: '?page=Invoices' },
  { name: 'Payments', href: '?page=Payments' },
  { name: 'Credit Notes', href: '?page=Credit Notes' },
]

function classNames(...classes) {
  return classes.filter(Boolean).join(' ')
}

// Component that uses useSearchParams - needs to be wrapped in Suspense
const BillingAndPaymentsContent = () => {
  const params = useSearchParams()
  const currentPage = params.get('page') || 'Sales Orders' // Default to 'Sales Orders'
  const router = useRouter()

  const handleTabClick = (tabHref) => {
    router.push(tabHref) // Use Next.js router to navigate without refreshing the page
  }

  const handleSelectChange = (event) => {
    const selectedHref = event.target.value
    router.push(selectedHref) // Navigate using router for mobile dropdown
  }

  return (
    <>
      {/* <AccountStatementStats /> */}
      <div className="pb-6 mt-2 align-baseline sm:flex sm:justify-between">
        <div className="flex flex-col sm:flex-row">
          <p className="text-2xl font-bold">Billing & Payments</p>
        </div>
      </div>
      <div className="lg:pb-1">
        <div className="sm:hidden">
          <label htmlFor="tabs" className="sr-only">
            Select a tab
          </label>
          <Select value={`?page=${currentPage}`} onChange={handleSelectChange} name="billingAndPayments">
            {tabs.map((tab) => (
              <option key={tab.name} value={tab.href}>
                {tab.name}
              </option>
            ))}
          </Select>
          {/* <select
            id="tabs"
            name="tabs"
            value={`?page=${currentPage}`} // Keep the value synced with currentPage
            onChange={handleSelectChange}
            className="block w-full rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
          >
            {tabs.map((tab) => (
              <option key={tab.name} value={tab.href}>
                {tab.name}
              </option>
            ))}
          </select> */}
        </div>
        <div className="hidden sm:block">
          <nav aria-label="Tabs" className="flex space-x-4">
            {tabs.map((tab) => (
              <button
                key={tab.name}
                onClick={() => handleTabClick(tab.href)}
                className={classNames(
                  tab.name === currentPage ? 'bg-indigo-100 text-indigo-700' : 'text-gray-500 hover:text-gray-700',
                  'rounded-md px-3 py-2 text-sm font-medium'
                )}
              >
                {tab.name}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {currentPage === 'Sales Orders' && <SalesOrderPage />}
      {currentPage === 'Invoices' && <InvoicesPage />}
      {currentPage === 'Dues' && <DuesPage />}
      {currentPage === 'Payments' && <TransactionsPage />}
      {currentPage === 'Credit Notes' && <CreditNotesPage />}
    </>
  )
}

// Main component with Suspense boundary
const BillingAndPayments = () => {
  return (
    <Suspense fallback={<div className="flex items-center justify-center p-8">Loading...</div>}>
      <BillingAndPaymentsContent />
    </Suspense>
  )
}

export default BillingAndPayments
