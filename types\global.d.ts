export {}

// Import payment types
import type * as PaymentTypes from '../src/types/payment'

declare global {
  interface Window {
    clarity: any
  }

  // Global payment namespace
  namespace Payment {
    // Re-export payment types for global access
    type Request = PaymentTypes.PaymentRequest
    type Session = PaymentTypes.PaymentSession
    type Transaction = PaymentTypes.PaymentTransaction
    type Status = PaymentTypes.PaymentStatus
    type RefundRequest = PaymentTypes.RefundRequest
    type WebhookEvent = PaymentTypes.WebhookEvent
    type ApiResponse<T = any> = PaymentTypes.ApiResponse<T>
    type HealthCheck = PaymentTypes.HealthCheckResponse
    type Config = PaymentTypes.ZohoPaymentConfig
  }
}
